#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار ميزة حذف الكروت الناجحة للكرت الواحد
تاريخ الإنشاء: 2025-07-23
الهدف: التحقق من أن ميزة حذف الكروت الناجحة تعمل بشكل صحيح للكرت الواحد
"""

import re

def test_button_logic():
    """اختبار منطق عرض زر حذف الكروت الناجحة"""
    print("🔍 اختبار منطق عرض زر حذف الكروت الناجحة...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_details_to_telegram
    func_match = re.search(r'def send_single_card_details_to_telegram.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_details_to_telegram")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من وجود منطق عرض الزر
    required_patterns = [
        'show_delete_successful_button.*=',
        'failed_count > 0 and',
        'success_count > 0 and',
        'single_card_successful_cards',
        'system_type.*hotspot'
    ]
    
    for pattern in required_patterns:
        if re.search(pattern, func_code, re.IGNORECASE):
            print(f"✅ منطق موجود: {pattern}")
        else:
            print(f"❌ منطق مفقود: {pattern}")
            return False
    
    return True

def test_button_text():
    """اختبار النص والزر"""
    print("\n🔍 اختبار النص والزر...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_details_to_telegram
    func_match = re.search(r'def send_single_card_details_to_telegram.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_details_to_telegram")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من وجود النص والزر
    required_elements = [
        'خيار حذف الكروت المرسلة بنجاح',
        'حذف الكروت المرسلة بنجاح من هذه العملية',
        'single_card_delete_successful_'
    ]
    
    for element in required_elements:
        if element in func_code:
            print(f"✅ عنصر موجود: {element}")
        else:
            print(f"❌ عنصر مفقود: {element}")
            return False
    
    return True

def test_functions():
    """اختبار وجود الدوال"""
    print("\n🔍 اختبار وجود الدوال...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # الدوال المطلوبة
    required_functions = [
        'def handle_single_card_delete_successful_request',
        'def execute_single_card_delete_successful',
        'def cancel_single_card_delete_successful'
    ]
    
    for func in required_functions:
        if func in content:
            print(f"✅ دالة موجودة: {func}")
        else:
            print(f"❌ دالة مفقودة: {func}")
            return False
    
    return True

def test_callbacks():
    """اختبار معالجات callback"""
    print("\n🔍 اختبار معالجات callback...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة process_telegram_callback
    func_match = re.search(r'def process_telegram_callback.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة process_telegram_callback")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من وجود معالجات callback
    required_callbacks = [
        'single_card_delete_successful_',
        'handle_single_card_delete_successful_request',
        'execute_single_card_delete_successful',
        'cancel_single_card_delete_successful'
    ]
    
    for callback in required_callbacks:
        if callback in func_code:
            print(f"✅ معالج callback موجود: {callback}")
        else:
            print(f"❌ معالج callback مفقود: {callback}")
            return False
    
    return True

def test_data_saving():
    """اختبار آلية حفظ البيانات"""
    print("\n🔍 اختبار آلية حفظ البيانات...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_to_mikrotik_silent
    func_match = re.search(r'def send_single_card_to_mikrotik_silent.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_to_mikrotik_silent")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من آلية حفظ البيانات
    required_data_elements = [
        'single_card_successful_cards',
        'single_card_successful_cards_info',
        'successful_usernames',
        'operation_type.*single_card'
    ]
    
    for element in required_data_elements:
        if re.search(element, func_code):
            print(f"✅ عنصر بيانات موجود: {element}")
        else:
            print(f"❌ عنصر بيانات مفقود: {element}")
            return False
    
    return True

def main():
    """تشغيل جميع الاختبارات"""
    print("🧪 بدء اختبار ميزة حذف الكروت الناجحة للكرت الواحد")
    print("="*60)
    
    tests = [
        ("منطق عرض الزر", test_button_logic),
        ("النص والزر", test_button_text),
        ("وجود الدوال", test_functions),
        ("معالجات callback", test_callbacks),
        ("آلية حفظ البيانات", test_data_saving)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                print(f"✅ {test_name}: نجح")
                passed += 1
            else:
                print(f"❌ {test_name}: فشل")
                failed += 1
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {str(e)}")
            failed += 1
        print("-" * 40)
    
    print("\n" + "="*60)
    print("📊 نتائج الاختبار:")
    print(f"✅ نجح: {passed}")
    print(f"❌ فشل: {failed}")
    print(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
    
    if failed == 0:
        print("🎉 تم تطبيق ميزة حذف الكروت الناجحة للكرت الواحد بنجاح!")
        print("💡 الميزة جاهزة للاستخدام")
        
        print("\n🎯 الميزات المُطبقة:")
        print("✅ منطق عرض الزر بناءً على الشروط المحددة")
        print("✅ النص التوضيحي والزر في واجهة المستخدم")
        print("✅ معالجات callback شاملة")
        print("✅ دوال معالجة وتنفيذ وإلغاء الحذف")
        print("✅ آلية حفظ البيانات للكروت الناجحة")
        print("✅ استخدام الدالة المشتركة delete_successful_cards_from_mikrotik")
        
        print("\n🔒 الشروط المُطبقة:")
        print("✅ يظهر الزر فقط عند وجود كروت ناجحة وفاشلة")
        print("✅ يعمل فقط مع نظام HotSpot")
        print("✅ نفس تجربة المستخدم الموجودة في البرق")
        
    else:
        print("⚠️ بعض المكونات تحتاج إلى مراجعة.")
    
    return failed == 0

if __name__ == "__main__":
    main()
