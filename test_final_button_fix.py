#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نهائي لإصلاح زر حذف الكروت الناجحة للكرت الواحد
تاريخ الإنشاء: 2025-07-23
الهدف: التحقق من أن الزر سيظهر الآن حتى في حالة النجاح الكامل للاختبار
"""

import re

def test_enhanced_button_logic():
    """اختبار منطق الزر المحسن"""
    print("🔍 اختبار منطق الزر المحسن...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_details_to_telegram
    func_match = re.search(r'def send_single_card_details_to_telegram.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_details_to_telegram")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من المنطق المحسن
    required_elements = [
        'show_delete_successful_button.*=.*False',
        'تشخيص شروط زر حذف الكروت الناجحة',
        'force_show_for_testing',
        'للاختبار.*إظهار الزر حتى لو لم يكن هناك فشل',
        'إنشاء قائمة الكروت الناجحة إذا لم تكن موجودة',
        'استخراج أسماء المستخدمين الناجحين من generated_credentials',
        'إظهار زر حذف الكروت الناجحة للاختبار.*نجاح كامل',
        'فشل جزئي'
    ]
    
    missing_elements = []
    for element in required_elements:
        if re.search(element, func_code, re.IGNORECASE):
            print(f"✅ عنصر موجود: {element}")
        else:
            print(f"❌ عنصر مفقود: {element}")
            missing_elements.append(element)
    
    if not missing_elements:
        print("✅ جميع العناصر المحسنة موجودة!")
        return True
    else:
        print(f"❌ عناصر مفقودة: {len(missing_elements)}")
        return False

def test_data_saving_enhanced():
    """اختبار حفظ البيانات المحسن"""
    print("\n🔍 اختبار حفظ البيانات المحسن...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_to_mikrotik_silent
    func_match = re.search(r'def send_single_card_to_mikrotik_silent.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_to_mikrotik_silent")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من حفظ البيانات المحسن
    required_elements = [
        'تشخيص حفظ الكروت الناجحة للكرت الواحد',
        'حفظ دائماً إذا كان هناك كروت ناجحة.*بغض النظر عن الفاشلة للاختبار',
        'success_count.*>.*0.*and.*system_type.*hotspot',
        'إذا لم نجد في successful_cards، استخدم generated_credentials',
        'قائمة الكروت الناجحة المحفوظة'
    ]
    
    missing_elements = []
    for element in required_elements:
        if re.search(element, func_code, re.IGNORECASE):
            print(f"✅ عنصر موجود: {element}")
        else:
            print(f"❌ عنصر مفقود: {element}")
            missing_elements.append(element)
    
    if not missing_elements:
        print("✅ جميع عناصر حفظ البيانات المحسنة موجودة!")
        return True
    else:
        print(f"❌ عناصر مفقودة: {len(missing_elements)}")
        return False

def test_button_text_and_callback():
    """اختبار نص الزر وcallback"""
    print("\n🔍 اختبار نص الزر وcallback...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_details_to_telegram
    func_match = re.search(r'def send_single_card_details_to_telegram.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_details_to_telegram")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من نص الزر
    button_elements = [
        'حذف الكروت المرسلة بنجاح من هذه العملية',
        'single_card_delete_successful_.*success_count',
        'keyboard_buttons.*append',
        'show_delete_successful_button'
    ]
    
    missing_elements = []
    for element in button_elements:
        if re.search(element, func_code, re.IGNORECASE):
            print(f"✅ عنصر زر موجود: {element}")
        else:
            print(f"❌ عنصر زر مفقود: {element}")
            missing_elements.append(element)
    
    if not missing_elements:
        print("✅ جميع عناصر الزر موجودة!")
        return True
    else:
        print(f"❌ عناصر زر مفقودة: {len(missing_elements)}")
        return False

def test_callback_handlers():
    """اختبار معالجات callback"""
    print("\n🔍 اختبار معالجات callback...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من وجود الدوال
    required_functions = [
        'def handle_single_card_delete_successful_request',
        'def execute_single_card_delete_successful',
        'def cancel_single_card_delete_successful'
    ]
    
    missing_functions = []
    for func in required_functions:
        if func in content:
            print(f"✅ دالة موجودة: {func}")
        else:
            print(f"❌ دالة مفقودة: {func}")
            missing_functions.append(func)
    
    # التحقق من معالجة callback
    callback_match = re.search(r'elif callback_data\.startswith\("single_card_delete_successful_"\):', content)
    if callback_match:
        print("✅ معالج callback موجود: single_card_delete_successful_")
    else:
        print("❌ معالج callback مفقود: single_card_delete_successful_")
        missing_functions.append("callback handler")
    
    if not missing_functions:
        print("✅ جميع معالجات callback موجودة!")
        return True
    else:
        print(f"❌ معالجات مفقودة: {len(missing_functions)}")
        return False

def simulate_test_scenarios():
    """محاكاة سيناريوهات الاختبار"""
    print("\n🧪 محاكاة سيناريوهات الاختبار...")
    
    scenarios = [
        {
            "name": "نجاح كامل (للاختبار)",
            "success_count": 3,
            "failed_count": 0,
            "system_type": "hotspot",
            "expected": True,
            "reason": "force_show_for_testing"
        },
        {
            "name": "فشل جزئي",
            "success_count": 2,
            "failed_count": 1,
            "system_type": "hotspot",
            "expected": True,
            "reason": "شروط عادية"
        },
        {
            "name": "فشل كامل",
            "success_count": 0,
            "failed_count": 3,
            "system_type": "hotspot",
            "expected": False,
            "reason": "لا توجد كروت ناجحة"
        },
        {
            "name": "User Manager",
            "success_count": 2,
            "failed_count": 1,
            "system_type": "usermanager",
            "expected": False,
            "reason": "ليس HotSpot"
        }
    ]
    
    all_correct = True
    
    for scenario in scenarios:
        print(f"\n📋 سيناريو: {scenario['name']}")
        print(f"   - success_count: {scenario['success_count']}")
        print(f"   - failed_count: {scenario['failed_count']}")
        print(f"   - system_type: {scenario['system_type']}")
        
        # محاكاة الشروط
        force_show = (scenario['success_count'] > 0 and scenario['system_type'] == 'hotspot')
        normal_show = (scenario['failed_count'] > 0 and scenario['success_count'] > 0 and scenario['system_type'] == 'hotspot')
        
        should_show = normal_show or force_show
        
        print(f"   - force_show_for_testing: {force_show}")
        print(f"   - normal_conditions: {normal_show}")
        print(f"   - should_show: {should_show}")
        print(f"   - expected: {scenario['expected']}")
        
        if should_show == scenario['expected']:
            print(f"   ✅ صحيح - {scenario['reason']}")
        else:
            print(f"   ❌ خطأ - متوقع {scenario['expected']} لكن حصل على {should_show}")
            all_correct = False
    
    return all_correct

def main():
    """تشغيل جميع الاختبارات"""
    print("🧪 بدء الاختبار النهائي لإصلاح زر حذف الكروت الناجحة")
    print("="*65)
    
    tests = [
        ("منطق الزر المحسن", test_enhanced_button_logic),
        ("حفظ البيانات المحسن", test_data_saving_enhanced),
        ("نص الزر وcallback", test_button_text_and_callback),
        ("معالجات callback", test_callback_handlers),
        ("محاكاة السيناريوهات", simulate_test_scenarios)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                print(f"✅ {test_name}: نجح")
                passed += 1
            else:
                print(f"❌ {test_name}: فشل")
                failed += 1
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {str(e)}")
            failed += 1
        print("-" * 45)
    
    print("\n" + "="*65)
    print("📊 نتائج الاختبار:")
    print(f"✅ نجح: {passed}")
    print(f"❌ فشل: {failed}")
    print(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
    
    if failed == 0:
        print("🎉 تم إصلاح زر حذف الكروت الناجحة بنجاح!")
        print("💡 الزر سيظهر الآن في جميع الحالات المناسبة")
        
        print("\n🎯 الإصلاحات المُطبقة:")
        print("✅ إضافة خيار اختبار لإظهار الزر حتى مع النجاح الكامل")
        print("✅ تحسين منطق حفظ البيانات")
        print("✅ إضافة تسجيل مفصل للتشخيص")
        print("✅ تحسين استخراج أسماء المستخدمين")
        
        print("\n🔍 كيفية الاختبار:")
        print("1. جرب إنشاء كرت واحد في نظام HotSpot")
        print("2. الزر سيظهر حتى لو نجح الكرت بالكامل (للاختبار)")
        print("3. راقب رسائل السجل للتشخيص")
        print("4. اضغط على الزر لاختبار الوظيفة")
        
    else:
        print("⚠️ بعض المكونات تحتاج إلى مراجعة.")
    
    return failed == 0

if __name__ == "__main__":
    main()
