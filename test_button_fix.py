#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إصلاح زر حذف الكروت الناجحة للكرت الواحد
تاريخ الإنشاء: 2025-07-23
الهدف: التحقق من أن الزر سيظهر في المكان الصحيح
"""

import re

def test_final_report_button():
    """اختبار وجود الزر في دالة send_cards_final_report"""
    print("🔍 اختبار وجود الزر في دالة send_cards_final_report...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_cards_final_report
    func_match = re.search(r'def send_cards_final_report.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_cards_final_report")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من وجود الزر
    required_elements = [
        'card_type == "single"',
        'success_count > 0',
        'single_card_successful_cards',
        'حذف الكروت الناجحة المرسلة للميكروتيك',
        'single_card_delete_successful_'
    ]
    
    for element in required_elements:
        if element in func_code:
            print(f"✅ عنصر موجود: {element}")
        else:
            print(f"❌ عنصر مفقود: {element}")
            return False
    
    return True

def test_data_saving_fix():
    """اختبار إصلاح آلية حفظ البيانات"""
    print("\n🔍 اختبار إصلاح آلية حفظ البيانات...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_to_mikrotik_silent
    func_match = re.search(r'def send_single_card_to_mikrotik_silent.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_to_mikrotik_silent")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من آلية حفظ البيانات
    required_data_elements = [
        'successful_cards.*=.*\[\]',
        'failed_cards.*=.*\[\]',
        'single_card_successful_cards.*=',
        'successful_usernames'
    ]
    
    for element in required_data_elements:
        if re.search(element, func_code):
            print(f"✅ عنصر بيانات موجود: {element}")
        else:
            print(f"❌ عنصر بيانات مفقود: {element}")
            return False
    
    return True

def test_callback_handlers():
    """اختبار معالجات callback"""
    print("\n🔍 اختبار معالجات callback...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة process_telegram_callback
    func_match = re.search(r'def process_telegram_callback.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة process_telegram_callback")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من وجود معالجات callback
    required_callbacks = [
        'single_card_delete_successful_',
        'handle_single_card_delete_successful_request',
        'execute_single_card_delete_successful',
        'cancel_single_card_delete_successful'
    ]
    
    for callback in required_callbacks:
        if callback in func_code:
            print(f"✅ معالج callback موجود: {callback}")
        else:
            print(f"❌ معالج callback مفقود: {callback}")
            return False
    
    return True

def test_functions_exist():
    """اختبار وجود الدوال المطلوبة"""
    print("\n🔍 اختبار وجود الدوال المطلوبة...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # الدوال المطلوبة
    required_functions = [
        'def handle_single_card_delete_successful_request',
        'def execute_single_card_delete_successful',
        'def cancel_single_card_delete_successful'
    ]
    
    for func in required_functions:
        if func in content:
            print(f"✅ دالة موجودة: {func}")
        else:
            print(f"❌ دالة مفقودة: {func}")
            return False
    
    return True

def main():
    """تشغيل جميع الاختبارات"""
    print("🧪 بدء اختبار إصلاح زر حذف الكروت الناجحة للكرت الواحد")
    print("="*65)
    
    tests = [
        ("الزر في التقرير النهائي", test_final_report_button),
        ("إصلاح آلية حفظ البيانات", test_data_saving_fix),
        ("معالجات callback", test_callback_handlers),
        ("وجود الدوال المطلوبة", test_functions_exist)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                print(f"✅ {test_name}: نجح")
                passed += 1
            else:
                print(f"❌ {test_name}: فشل")
                failed += 1
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {str(e)}")
            failed += 1
        print("-" * 45)
    
    print("\n" + "="*65)
    print("📊 نتائج الاختبار:")
    print(f"✅ نجح: {passed}")
    print(f"❌ فشل: {failed}")
    print(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
    
    if failed == 0:
        print("🎉 تم إصلاح زر حذف الكروت الناجحة للكرت الواحد بنجاح!")
        print("💡 الزر سيظهر الآن في التقرير النهائي")
        
        print("\n🎯 الإصلاحات المُطبقة:")
        print("✅ إضافة الزر في دالة send_cards_final_report")
        print("✅ آلية حفظ البيانات تعمل بشكل صحيح")
        print("✅ معالجات callback جاهزة")
        print("✅ جميع الدوال المطلوبة موجودة")
        
        print("\n🔒 شروط ظهور الزر:")
        print("✅ نوع الكرت = single")
        print("✅ وجود كروت ناجحة (success_count > 0)")
        print("✅ وجود كروت فاشلة (failed_count > 0)")
        print("✅ وجود بيانات محفوظة (single_card_successful_cards)")
        
    else:
        print("⚠️ بعض المكونات تحتاج إلى مراجعة.")
    
    return failed == 0

if __name__ == "__main__":
    main()
