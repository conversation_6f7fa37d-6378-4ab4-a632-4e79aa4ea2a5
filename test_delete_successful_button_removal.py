#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار حذف زر حذف الكروت الناجحة من ميزة الكرت الواحد
تاريخ الإنشاء: 2025-07-23
الهدف: التحقق من أن زر حذف الكروت الناجحة تم حذفه بالكامل من ميزة الكرت الواحد
"""

import re

def test_button_logic_removal():
    """اختبار حذف منطق عرض زر حذف الكروت الناجحة"""
    print("🔍 اختبار حذف منطق عرض زر حذف الكروت الناجحة...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_details_to_telegram
    func_match = re.search(r'def send_single_card_details_to_telegram.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_details_to_telegram")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من أن منطق الزر تم حذفه
    removed_patterns = [
        'شروط إظهار زر حذف الكروت المرسلة بنجاح للكرت الواحد (نسخة من البرق)',
        'تشخيص شروط زر الحذف للكرت الواحد (نسخة من البرق)',
        'hasattr single_card_lightning_successful_cards',
        'bool(single_card_lightning_successful_cards)',
        'failed_count > 0 and.*success_count > 0 and.*hasattr.*single_card_lightning_successful_cards'
    ]
    
    for pattern in removed_patterns:
        if re.search(pattern, func_code):
            print(f"❌ نمط لم يتم حذفه: {pattern}")
            return False
        print(f"✅ نمط تم حذفه بنجاح: {pattern}")
    
    # التحقق من وجود التعليق الجديد
    if 'تم حذف زر حذف الكروت الناجحة للكرت الواحد بناءً على طلب المستخدم' not in func_code:
        print("❌ التعليق الجديد غير موجود")
        return False
    print("✅ التعليق الجديد موجود")
    
    # التحقق من أن show_delete_successful_button = False
    if 'show_delete_successful_button = False' not in func_code:
        print("❌ show_delete_successful_button = False غير موجود")
        return False
    print("✅ show_delete_successful_button = False موجود")
    
    return True

def test_button_text_removal():
    """اختبار حذف النص المتعلق بزر حذف الكروت الناجحة"""
    print("\n🔍 اختبار حذف النص المتعلق بزر حذف الكروت الناجحة...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_details_to_telegram
    func_match = re.search(r'def send_single_card_details_to_telegram.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_details_to_telegram")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من أن النص تم حذفه
    removed_text_patterns = [
        'خيار حذف الكروت المرسلة بنجاح:',
        'نظراً لوجود {failed_count} كرت فاشل، يمكنك اختيار حذف',
        'هذا الخيار يحذف فقط الكروت المرسلة بنجاح من عملية الكرت الواحد الحالية'
    ]
    
    for pattern in removed_text_patterns:
        if pattern in func_code:
            print(f"❌ نص لم يتم حذفه: {pattern}")
            return False
        print(f"✅ نص تم حذفه بنجاح: {pattern}")
    
    # التحقق من وجود التعليق الجديد
    if 'تم حذف النص المتعلق بزر حذف الكروت الناجحة بناءً على طلب المستخدم' not in func_code:
        print("❌ التعليق الجديد للنص غير موجود")
        return False
    print("✅ التعليق الجديد للنص موجود")
    
    return True

def test_button_keyboard_removal():
    """اختبار حذف الزر من لوحة المفاتيح"""
    print("\n🔍 اختبار حذف الزر من لوحة المفاتيح...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_details_to_telegram
    func_match = re.search(r'def send_single_card_details_to_telegram.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_details_to_telegram")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من أن الزر تم حذفه من لوحة المفاتيح
    removed_button_patterns = [
        '"text": f"🗑️ حذف الكروت المرسلة بنجاح من هذه العملية',
        '"callback_data": f"single_card_lightning_delete_successful_{success_count}"',
        'keyboard_buttons.append.*single_card_lightning_delete_successful'
    ]
    
    for pattern in removed_button_patterns:
        if re.search(pattern, func_code):
            print(f"❌ زر لم يتم حذفه: {pattern}")
            return False
        print(f"✅ زر تم حذفه بنجاح: {pattern}")
    
    # التحقق من وجود التعليق الجديد
    if 'تم حذف زر حذف الكروت الناجحة من لوحة المفاتيح بناءً على طلب المستخدم' not in func_code:
        print("❌ التعليق الجديد للزر غير موجود")
        return False
    print("✅ التعليق الجديد للزر موجود")
    
    return True

def test_log_message_removal():
    """اختبار حذف رسالة السجل المتعلقة بالزر"""
    print("\n🔍 اختبار حذف رسالة السجل المتعلقة بالزر...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_details_to_telegram
    func_match = re.search(r'def send_single_card_details_to_telegram.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_details_to_telegram")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من أن رسالة السجل تم حذفها
    removed_log_patterns = [
        'تم إضافة زر حذف الكروت المرسلة بنجاح للكرت الواحد (نسخة من البرق)',
        'self.logger.info.*زر حذف الكروت المرسلة بنجاح للكرت الواحد'
    ]
    
    for pattern in removed_log_patterns:
        if re.search(pattern, func_code):
            print(f"❌ رسالة سجل لم يتم حذفها: {pattern}")
            return False
        print(f"✅ رسالة سجل تم حذفها بنجاح: {pattern}")
    
    # التحقق من وجود التعليق الجديد
    if 'تم حذف رسالة السجل المتعلقة بزر حذف الكروت الناجحة بناءً على طلب المستخدم' not in func_code:
        print("❌ التعليق الجديد لرسالة السجل غير موجود")
        return False
    print("✅ التعليق الجديد لرسالة السجل موجود")
    
    return True

def test_data_saving_removal():
    """اختبار حذف آلية حفظ البيانات المتعلقة بالزر"""
    print("\n🔍 اختبار حذف آلية حفظ البيانات المتعلقة بالزر...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_to_mikrotik_silent
    func_match = re.search(r'def send_single_card_to_mikrotik_silent.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_to_mikrotik_silent")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من أن آلية حفظ البيانات تم حذفها
    removed_data_patterns = [
        'حفظ قائمة الكروت الناجحة للاستخدام في حذف الكروت المرسلة بنجاح (نسخة من البرق)',
        'تشخيص حفظ الكروت الناجحة للكرت الواحد (نسخة من البرق)',
        'self.single_card_lightning_successful_cards = successful_usernames.copy()',
        'self.single_card_lightning_successful_cards_info = {',
        'operation_type.*single_card_lightning',
        'successful_usernames.*نسخة من البرق'
    ]
    
    for pattern in removed_data_patterns:
        if re.search(pattern, func_code):
            print(f"❌ آلية حفظ بيانات لم يتم حذفها: {pattern}")
            return False
        print(f"✅ آلية حفظ بيانات تم حذفها بنجاح: {pattern}")
    
    # التحقق من وجود التعليق الجديد
    if 'تم حذف آلية حفظ البيانات المتعلقة بزر حذف الكروت الناجحة بناءً على طلب المستخدم' not in func_code:
        print("❌ التعليق الجديد لآلية حفظ البيانات غير موجود")
        return False
    print("✅ التعليق الجديد لآلية حفظ البيانات موجود")
    
    return True

def test_stats_cleanup():
    """اختبار تنظيف إحصائيات الإرسال من البيانات المتعلقة بالزر"""
    print("\n🔍 اختبار تنظيف إحصائيات الإرسال من البيانات المتعلقة بالزر...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_to_mikrotik_silent
    func_match = re.search(r'def send_single_card_to_mikrotik_silent.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_to_mikrotik_silent")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من أن البيانات الإضافية تم حذفها من الإحصائيات
    removed_stats_patterns = [
        'successful_usernames.*نسخة من البرق',
        'إضافة قائمة المستخدمين الناجحين.*نسخة من البرق'
    ]
    
    for pattern in removed_stats_patterns:
        if re.search(pattern, func_code):
            print(f"❌ بيانات إحصائيات لم يتم حذفها: {pattern}")
            return False
        print(f"✅ بيانات إحصائيات تم حذفها بنجاح: {pattern}")
    
    # التحقق من أن الإحصائيات الأساسية ما زالت موجودة
    basic_stats_patterns = [
        'self.last_send_stats = {',
        "'success': success_count,",
        "'failed': failed_count,",
        "'duplicates': 0,",
        "'total': total"
    ]
    
    for pattern in basic_stats_patterns:
        if pattern not in func_code:
            print(f"❌ إحصائيات أساسية مفقودة: {pattern}")
            return False
        print(f"✅ إحصائيات أساسية موجودة: {pattern}")
    
    return True

def main():
    """تشغيل جميع الاختبارات"""
    print("🧪 بدء اختبار حذف زر حذف الكروت الناجحة من ميزة الكرت الواحد")
    print("="*80)
    
    tests = [
        ("حذف منطق عرض الزر", test_button_logic_removal),
        ("حذف النص المتعلق بالزر", test_button_text_removal),
        ("حذف الزر من لوحة المفاتيح", test_button_keyboard_removal),
        ("حذف رسالة السجل", test_log_message_removal),
        ("حذف آلية حفظ البيانات", test_data_saving_removal),
        ("تنظيف إحصائيات الإرسال", test_stats_cleanup)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                print(f"✅ {test_name}: نجح")
                passed += 1
            else:
                print(f"❌ {test_name}: فشل")
                failed += 1
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {str(e)}")
            failed += 1
        print("-" * 40)
    
    print("\n" + "="*80)
    print("📊 نتائج الاختبار:")
    print(f"✅ نجح: {passed}")
    print(f"❌ فشل: {failed}")
    print(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
    
    if failed == 0:
        print("🎉 تم حذف زر حذف الكروت الناجحة من ميزة الكرت الواحد بنجاح!")
        print("💡 الزر لن يظهر بعد الآن عند حدوث فشل في الكرت الواحد")
        
        print("\n🎯 ما تم حذفه:")
        print("✅ منطق عرض الزر وشروطه")
        print("✅ النص المتعلق بالزر")
        print("✅ الزر من لوحة المفاتيح")
        print("✅ رسالة السجل المتعلقة بالزر")
        print("✅ آلية حفظ البيانات المتعلقة بالزر")
        print("✅ البيانات الإضافية من إحصائيات الإرسال")
        
        print("\n🔒 ما تم الحفاظ عليه:")
        print("✅ زر إعادة المحاولة للكروت الفاشلة")
        print("✅ الإحصائيات الأساسية للإرسال")
        print("✅ باقي وظائف الكرت الواحد")
        
    else:
        print("⚠️ بعض المكونات تحتاج إلى مراجعة.")
    
    return failed == 0

if __name__ == "__main__":
    main()
