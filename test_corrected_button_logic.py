#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار شروط ظهور زر حذف الكروت الناجحة المصححة
تاريخ الإنشاء: 2025-07-23
الهدف: التحقق من أن الشروط تعكس المتطلبات المحددة بدقة
"""

import re

def test_corrected_button_conditions():
    """اختبار الشروط المصححة لظهور الزر"""
    print("🔍 اختبار الشروط المصححة لظهور زر حذف الكروت الناجحة...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_details_to_telegram
    func_match = re.search(r'def send_single_card_details_to_telegram.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_details_to_telegram")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من الشروط المصححة
    required_conditions = [
        'مصححة حسب المتطلبات',
        'condition1.*=.*failed_count.*>.*0',
        'condition2.*=.*success_count.*>.*0',
        'condition3.*=.*system_type.*==.*hotspot',
        'condition4.*=.*hasattr.*single_card_successful_cards',
        'الشرط 1.*وجود كروت فاشلة',
        'الشرط 2.*وجود كروت ناجحة',
        'الشرط 3.*نظام HotSpot',
        'الشرط 4.*بيانات محفوظة',
        'condition1 and condition2 and condition3 and condition4'
    ]
    
    missing_conditions = []
    for condition in required_conditions:
        if re.search(condition, func_code, re.IGNORECASE):
            print(f"✅ شرط موجود: {condition}")
        else:
            print(f"❌ شرط مفقود: {condition}")
            missing_conditions.append(condition)
    
    # التحقق من عدم وجود خيار الاختبار القديم
    old_testing_logic = [
        'force_show_for_testing',
        'للاختبار.*إظهار الزر حتى لو لم يكن هناك فشل',
        'نجاح كامل'
    ]
    
    for old_logic in old_testing_logic:
        if re.search(old_logic, func_code, re.IGNORECASE):
            print(f"❌ منطق قديم ما زال موجود: {old_logic}")
            missing_conditions.append(f"remove_{old_logic}")
        else:
            print(f"✅ تم إزالة المنطق القديم: {old_logic}")
    
    if not missing_conditions:
        print("✅ جميع الشروط المصححة موجودة والمنطق القديم تم إزالته!")
        return True
    else:
        print(f"❌ مشاكل في الشروط: {len(missing_conditions)}")
        return False

def test_data_saving_conditions():
    """اختبار شروط حفظ البيانات المصححة"""
    print("\n🔍 اختبار شروط حفظ البيانات المصححة...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_to_mikrotik_silent
    func_match = re.search(r'def send_single_card_to_mikrotik_silent.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_to_mikrotik_silent")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من شروط حفظ البيانات المصححة
    required_saving_conditions = [
        'حفظ الكروت الناجحة فقط عند وجود فشل جزئي.*حسب المتطلبات المصححة',
        'failed_count.*>.*0.*and.*success_count.*>.*0.*and.*system_type.*hotspot'
    ]
    
    # التحقق من عدم وجود الشروط القديمة
    old_saving_conditions = [
        'حفظ دائماً إذا كان هناك كروت ناجحة.*بغض النظر عن الفاشلة للاختبار',
        'success_count.*>.*0.*and.*system_type.*hotspot.*(?!.*failed_count)'
    ]
    
    missing_conditions = []
    
    for condition in required_saving_conditions:
        if re.search(condition, func_code, re.IGNORECASE):
            print(f"✅ شرط حفظ صحيح: {condition}")
        else:
            print(f"❌ شرط حفظ مفقود: {condition}")
            missing_conditions.append(condition)
    
    for old_condition in old_saving_conditions:
        if re.search(old_condition, func_code, re.IGNORECASE):
            print(f"❌ شرط حفظ قديم ما زال موجود: {old_condition}")
            missing_conditions.append(f"remove_{old_condition}")
        else:
            print(f"✅ تم إزالة الشرط القديم: {old_condition}")
    
    if not missing_conditions:
        print("✅ شروط حفظ البيانات مصححة بشكل صحيح!")
        return True
    else:
        print(f"❌ مشاكل في شروط حفظ البيانات: {len(missing_conditions)}")
        return False

def simulate_scenarios():
    """محاكاة السيناريوهات المختلفة"""
    print("\n🧪 محاكاة السيناريوهات حسب المتطلبات المصححة...")
    
    scenarios = [
        {
            "name": "فشل جزئي - الحالة المثالية",
            "failed_count": 1,
            "success_count": 2,
            "system_type": "hotspot",
            "has_data": True,
            "expected": True,
            "reason": "جميع الشروط مستوفاة"
        },
        {
            "name": "نجاح كامل",
            "failed_count": 0,
            "success_count": 3,
            "system_type": "hotspot",
            "has_data": True,
            "expected": False,
            "reason": "لا توجد كروت فاشلة"
        },
        {
            "name": "فشل كامل",
            "failed_count": 3,
            "success_count": 0,
            "system_type": "hotspot",
            "has_data": False,
            "expected": False,
            "reason": "لا توجد كروت ناجحة"
        },
        {
            "name": "فشل جزئي في User Manager",
            "failed_count": 1,
            "success_count": 2,
            "system_type": "usermanager",
            "has_data": True,
            "expected": False,
            "reason": "ليس نظام HotSpot"
        },
        {
            "name": "فشل جزئي بدون بيانات محفوظة",
            "failed_count": 1,
            "success_count": 2,
            "system_type": "hotspot",
            "has_data": False,
            "expected": True,
            "reason": "سيتم إنشاء البيانات تلقائياً"
        }
    ]
    
    all_correct = True
    
    for scenario in scenarios:
        print(f"\n📋 سيناريو: {scenario['name']}")
        print(f"   - failed_count: {scenario['failed_count']}")
        print(f"   - success_count: {scenario['success_count']}")
        print(f"   - system_type: {scenario['system_type']}")
        print(f"   - has_data: {scenario['has_data']}")
        
        # محاكاة الشروط المصححة
        condition1 = scenario['failed_count'] > 0
        condition2 = scenario['success_count'] > 0
        condition3 = scenario['system_type'] == 'hotspot'
        condition4 = scenario['has_data']
        
        # الشرط الأساسي
        basic_conditions = condition1 and condition2 and condition3
        
        # إذا كانت الشروط الأساسية مستوفاة لكن البيانات غير موجودة، سيتم إنشاؤها
        if basic_conditions and not condition4:
            condition4 = True  # سيتم إنشاء البيانات تلقائياً
        
        should_show = condition1 and condition2 and condition3 and condition4
        
        print(f"   - الشرط 1 (كروت فاشلة): {condition1}")
        print(f"   - الشرط 2 (كروت ناجحة): {condition2}")
        print(f"   - الشرط 3 (HotSpot): {condition3}")
        print(f"   - الشرط 4 (بيانات): {condition4}")
        print(f"   - should_show: {should_show}")
        print(f"   - expected: {scenario['expected']}")
        
        if should_show == scenario['expected']:
            print(f"   ✅ صحيح - {scenario['reason']}")
        else:
            print(f"   ❌ خطأ - متوقع {scenario['expected']} لكن حصل على {should_show}")
            all_correct = False
    
    return all_correct

def test_requirements_compliance():
    """اختبار مطابقة المتطلبات"""
    print("\n📋 اختبار مطابقة المتطلبات المحددة...")
    
    requirements = [
        "يجب أن يظهر الزر عند حدوث أي فشل في العملية، حتى لو كان كرت واحد فقط",
        "يجب أن يظهر الزر عندما يكون هناك كروت ناجحة وكروت فاشلة في نفس العملية",
        "يجب أن يعمل الزر في نظام HotSpot فقط"
    ]
    
    # اختبار المتطلب الأول
    req1_test = {
        "failed_count": 1,
        "success_count": 1,
        "system_type": "hotspot",
        "expected": True
    }
    
    # اختبار المتطلب الثاني
    req2_test = {
        "failed_count": 2,
        "success_count": 3,
        "system_type": "hotspot",
        "expected": True
    }
    
    # اختبار المتطلب الثالث
    req3_test = {
        "failed_count": 1,
        "success_count": 2,
        "system_type": "usermanager",
        "expected": False
    }
    
    tests = [req1_test, req2_test, req3_test]
    all_passed = True
    
    for i, test in enumerate(tests, 1):
        condition1 = test['failed_count'] > 0
        condition2 = test['success_count'] > 0
        condition3 = test['system_type'] == 'hotspot'
        condition4 = True  # افتراض وجود البيانات
        
        result = condition1 and condition2 and condition3 and condition4
        
        print(f"✅ المتطلب {i}: {requirements[i-1]}")
        print(f"   - النتيجة: {result}")
        print(f"   - المتوقع: {test['expected']}")
        
        if result == test['expected']:
            print(f"   ✅ مطابق للمتطلب")
        else:
            print(f"   ❌ غير مطابق للمتطلب")
            all_passed = False
    
    return all_passed

def main():
    """تشغيل جميع الاختبارات"""
    print("🧪 بدء اختبار شروط ظهور زر حذف الكروت الناجحة المصححة")
    print("="*70)
    
    tests = [
        ("الشروط المصححة", test_corrected_button_conditions),
        ("شروط حفظ البيانات", test_data_saving_conditions),
        ("محاكاة السيناريوهات", simulate_scenarios),
        ("مطابقة المتطلبات", test_requirements_compliance)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                print(f"✅ {test_name}: نجح")
                passed += 1
            else:
                print(f"❌ {test_name}: فشل")
                failed += 1
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {str(e)}")
            failed += 1
        print("-" * 50)
    
    print("\n" + "="*70)
    print("📊 نتائج الاختبار:")
    print(f"✅ نجح: {passed}")
    print(f"❌ فشل: {failed}")
    print(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
    
    if failed == 0:
        print("🎉 تم تصحيح شروط ظهور زر حذف الكروت الناجحة بنجاح!")
        print("💡 الزر سيظهر الآن حسب المتطلبات المحددة بدقة")
        
        print("\n🎯 الشروط المصححة:")
        print("✅ يظهر عند وجود كروت فاشلة وناجحة معاً")
        print("✅ يعمل في نظام HotSpot فقط")
        print("✅ لا يظهر مع النجاح الكامل")
        print("✅ لا يظهر مع الفشل الكامل")
        print("✅ لا يظهر في User Manager")
        
        print("\n🔍 كيفية الاختبار:")
        print("1. أنشئ عدة كروت في HotSpot")
        print("2. تأكد من حدوث فشل جزئي (بعض الكروت تنجح وبعضها يفشل)")
        print("3. الزر سيظهر فقط في هذه الحالة")
        
    else:
        print("⚠️ بعض الاختبارات فشلت - يحتاج مراجعة.")
    
    return failed == 0

if __name__ == "__main__":
    main()
