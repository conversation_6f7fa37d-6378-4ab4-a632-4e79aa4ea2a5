#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نسخ ميزة حذف الكروت الناجحة من البرق إلى الكرت الواحد
تاريخ الإنشاء: 2025-07-23
الهدف: التحقق من أن الوظيفة المنسوخة تعمل بنفس طريقة البرق
"""

import re

def test_data_saving_mechanism():
    """اختبار آلية حفظ البيانات المنسوخة من البرق"""
    print("🔍 اختبار آلية حفظ البيانات المنسوخة من البرق...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_to_mikrotik_silent
    func_match = re.search(r'def send_single_card_to_mikrotik_silent.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_to_mikrotik_silent")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من آلية حفظ البيانات المنسوخة من البرق
    saving_patterns = [
        'تشخيص حفظ الكروت الناجحة للكرت الواحد (نسخة من البرق)',
        'if failed_count > 0 and success_count > 0 and getattr(self, \'system_type\', \'\') == \'hotspot\':',
        'حفظ {success_count} كرت ناجح لعرض خيار حذف الكروت المرسلة بنجاح للكرت الواحد (نسخة من البرق)',
        'self.single_card_lightning_successful_cards = successful_usernames.copy()',
        'self.single_card_lightning_successful_cards_info = {',
        '\'operation_type\': \'single_card_lightning\'',
        '\'system_type\': \'hotspot\'',
        'successful_usernames\': [card.get(\'username\', \'\') for card in successful_cards if card.get(\'username\')]'
    ]
    
    for pattern in saving_patterns:
        if pattern not in func_code:
            print(f"❌ نمط حفظ البيانات غير موجود: {pattern}")
            return False
        print(f"✅ نمط حفظ البيانات موجود: {pattern}")
    
    return True

def test_notification_function():
    """اختبار دالة إرسال الإشعار مع الزر المنسوخة من البرق"""
    print("\n🔍 اختبار دالة إرسال الإشعار مع الزر المنسوخة من البرق...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن الدالة
    func_match = re.search(r'def send_single_card_notification_with_delete_successful_button.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_notification_with_delete_successful_button")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من عناصر الدالة المنسوخة من البرق
    required_elements = [
        'إرسال إشعار الكرت الواحد مع زر حذف الكروت المرسلة بنجاح (نسخة من البرق)',
        'خيار حذف الكروت المرسلة بنجاح:',
        'نظراً لوجود {failed_count} كرت فاشل',
        'يمكنك اختيار حذف الـ {success_count} كرت المرسل بنجاح',
        'هذا الخيار يحذف فقط الكروت المرسلة بنجاح من عملية الكرت الواحد الحالية',
        '"text": f"🗑️ حذف الكروت المرسلة بنجاح من هذه العملية ({success_count})"',
        '"callback_data": f"single_card_lightning_delete_successful_{success_count}"',
        'تم إرسال إشعار الكرت الواحد مع زر حذف الكروت المرسلة بنجاح'
    ]
    
    for element in required_elements:
        if element not in func_code:
            print(f"❌ عنصر مطلوب غير موجود: {element}")
            return False
        print(f"✅ عنصر مطلوب موجود: {element}")
    
    return True

def test_handle_request_function():
    """اختبار دالة معالجة طلب الحذف المنسوخة من البرق"""
    print("\n🔍 اختبار دالة معالجة طلب الحذف المنسوخة من البرق...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن الدالة
    func_match = re.search(r'def handle_single_card_lightning_delete_successful_request.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة handle_single_card_lightning_delete_successful_request")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من عناصر الدالة المنسوخة من البرق
    required_elements = [
        'معالجة طلب حذف الكروت المرسلة بنجاح للكرت الواحد - عرض تأكيد الحذف (نسخة من البرق)',
        'if not hasattr(self, \'single_card_lightning_successful_cards\')',
        'single_card_lightning_successful_cards_info.get(\'system_type\') != \'hotspot\'',
        'تأكيد حذف الكروت المرسلة بنجاح',
        'العملية المطلوبة:',
        'حذف الكروت المرسلة بنجاح من عملية الكرت الواحد الحالية',
        'النظام:</b> 🌐 HotSpot',
        'نوع العملية:</b> 🎴 الكرت الواحد',
        'single_card_lightning_delete_successful_confirm_',
        'single_card_lightning_delete_successful_cancel'
    ]
    
    for element in required_elements:
        if element not in func_code:
            print(f"❌ عنصر مطلوب غير موجود: {element}")
            return False
        print(f"✅ عنصر مطلوب موجود: {element}")
    
    return True

def test_execute_function():
    """اختبار دالة تنفيذ الحذف المنسوخة من البرق"""
    print("\n🔍 اختبار دالة تنفيذ الحذف المنسوخة من البرق...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن الدالة
    func_match = re.search(r'def execute_single_card_lightning_delete_successful.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة execute_single_card_lightning_delete_successful")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من عناصر التنفيذ المنسوخة من البرق
    execution_elements = [
        'تنفيذ عملية حذف الكروت المرسلة بنجاح للكرت الواحد من MikroTik (نسخة من البرق)',
        'بدء عملية حذف الكروت',
        'جاري حذف {cards_count} كرت من خادم MikroTik',
        'api = self.connect_api()',
        'فشل في الاتصال بخادم MikroTik',
        'self.delete_successful_cards_from_mikrotik(self.single_card_lightning_successful_cards, api)',
        'تم حذف الكروت المرسلة بنجاح!',
        'إحصائيات الحذف:',
        'نوع العملية:</b> 🎴 حذف الكروت المرسلة بنجاح من عملية الكرت الواحد',
        'self.single_card_lightning_successful_cards = []',
        'delattr(self, \'single_card_lightning_successful_cards_info\')'
    ]
    
    for element in execution_elements:
        if element not in func_code:
            print(f"❌ عنصر التنفيذ غير موجود: {element}")
            return False
        print(f"✅ عنصر التنفيذ موجود: {element}")
    
    return True

def test_cancel_function():
    """اختبار دالة إلغاء الحذف المنسوخة من البرق"""
    print("\n🔍 اختبار دالة إلغاء الحذف المنسوخة من البرق...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن الدالة
    func_match = re.search(r'def cancel_single_card_lightning_delete_successful.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة cancel_single_card_lightning_delete_successful")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من عناصر الإلغاء المنسوخة من البرق
    cancel_elements = [
        'إلغاء عملية حذف الكروت المرسلة بنجاح للكرت الواحد (نسخة من البرق)',
        'تم إلغاء حذف الكروت المرسلة بنجاح',
        'لم يتم حذف أي كروت',
        'جميع الكروت المرسلة بنجاح من عملية الكرت الواحد الحالية ما زالت موجودة',
        'يمكنك طلب حذف الكروت المرسلة بنجاح مرة أخرى'
    ]
    
    for element in cancel_elements:
        if element not in func_code:
            print(f"❌ عنصر الإلغاء غير موجود: {element}")
            return False
        print(f"✅ عنصر الإلغاء موجود: {element}")
    
    return True

def test_callback_handling():
    """اختبار معالجة callback المنسوخة من البرق"""
    print("\n🔍 اختبار معالجة callback المنسوخة من البرق...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من معالجة callback المنسوخة من البرق
    callback_patterns = [
        'elif callback_data.startswith("single_card_lightning_delete_successful_"):',
        'معالجة callback لحذف الكروت المرسلة بنجاح للكرت الواحد (نسخة من البرق)',
        'if callback_data.startswith("single_card_lightning_delete_successful_confirm_"):',
        'elif callback_data == "single_card_lightning_delete_successful_cancel":',
        'self.execute_single_card_lightning_delete_successful(bot_token, chat_id, cards_count)',
        'self.cancel_single_card_lightning_delete_successful(bot_token, chat_id)',
        'self.handle_single_card_lightning_delete_successful_request(bot_token, chat_id, success_count)'
    ]
    
    for pattern in callback_patterns:
        if pattern not in content:
            print(f"❌ نمط معالجة callback غير موجود: {pattern}")
            return False
        print(f"✅ نمط معالجة callback موجود: {pattern}")
    
    return True

def test_button_display_logic():
    """اختبار منطق عرض الزر المنسوخ من البرق"""
    print("\n🔍 اختبار منطق عرض الزر المنسوخ من البرق...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_details_to_telegram
    func_match = re.search(r'def send_single_card_details_to_telegram.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_details_to_telegram")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من منطق عرض الزر المنسوخ من البرق
    button_elements = [
        'شروط إظهار زر حذف الكروت المرسلة بنجاح للكرت الواحد (نسخة من البرق)',
        'تشخيص شروط زر الحذف للكرت الواحد (نسخة من البرق)',
        'hasattr single_card_lightning_successful_cards',
        'bool(single_card_lightning_successful_cards)',
        'show_delete_successful_button = (',
        'failed_count > 0 and',
        'success_count > 0 and',
        'hasattr(self, \'single_card_lightning_successful_cards\')',
        'خيار حذف الكروت المرسلة بنجاح:',
        'هذا الخيار يحذف فقط الكروت المرسلة بنجاح من عملية الكرت الواحد الحالية',
        '"text": f"🗑️ حذف الكروت المرسلة بنجاح من هذه العملية ({success_count})"',
        '"callback_data": f"single_card_lightning_delete_successful_{success_count}"',
        'تم إضافة زر حذف الكروت المرسلة بنجاح للكرت الواحد (نسخة من البرق)'
    ]
    
    for element in button_elements:
        if element not in func_code:
            print(f"❌ عنصر الزر غير موجود: {element}")
            return False
        print(f"✅ عنصر الزر موجود: {element}")
    
    return True

def main():
    """تشغيل جميع الاختبارات"""
    print("🧪 بدء اختبار نسخ ميزة حذف الكروت الناجحة من البرق إلى الكرت الواحد")
    print("="*80)
    
    tests = [
        ("آلية حفظ البيانات", test_data_saving_mechanism),
        ("دالة إرسال الإشعار مع الزر", test_notification_function),
        ("دالة معالجة طلب الحذف", test_handle_request_function),
        ("دالة تنفيذ الحذف", test_execute_function),
        ("دالة إلغاء الحذف", test_cancel_function),
        ("معالجة Callback", test_callback_handling),
        ("منطق عرض الزر", test_button_display_logic)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                print(f"✅ {test_name}: نجح")
                passed += 1
            else:
                print(f"❌ {test_name}: فشل")
                failed += 1
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {str(e)}")
            failed += 1
        print("-" * 40)
    
    print("\n" + "="*80)
    print("📊 نتائج الاختبار:")
    print(f"✅ نجح: {passed}")
    print(f"❌ فشل: {failed}")
    print(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
    
    if failed == 0:
        print("🎉 تم نسخ ميزة حذف الكروت الناجحة من البرق إلى الكرت الواحد بنجاح!")
        print("💡 الميزة تعمل الآن بنفس طريقة البرق تماماً")
        
        print("\n🎯 الميزات المنسوخة:")
        print("✅ آلية حفظ البيانات مطابقة للبرق")
        print("✅ دالة إرسال الإشعار مع الزر")
        print("✅ دالة معالجة طلب الحذف مع رسالة تأكيد")
        print("✅ دالة تنفيذ الحذف مع تقرير مفصل")
        print("✅ دالة إلغاء الحذف")
        print("✅ معالجة callback شاملة")
        print("✅ منطق عرض الزر مطابق للبرق")
        print("✅ استخدام نفس الدوال المشتركة")
        
    else:
        print("⚠️ بعض المكونات تحتاج إلى مراجعة.")
    
    return failed == 0

if __name__ == "__main__":
    main()
