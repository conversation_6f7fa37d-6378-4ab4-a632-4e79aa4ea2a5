#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
محاكاة اختبار الكرت الواحد مع فشل جزئي
تاريخ الإنشاء: 2025-07-23
الهدف: محاكاة حالة فشل جزئي لاختبار ظهور زر حذف الكروت الناجحة
"""

import sys
import os

# إضافة مسار الملف الرئيسي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def simulate_single_card_partial_failure():
    """محاكاة حالة فشل جزئي في الكرت الواحد"""
    print("🧪 محاكاة حالة فشل جزئي في الكرت الواحد...")
    
    try:
        # استيراد الكلاس الرئيسي
        from importlib import import_module
        
        # قراءة الملف الرئيسي
        main_file = "اخر حاجة  - كروت وبوت.py"
        
        with open(main_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن كلاس MikroTikCardGenerator
        import re
        class_match = re.search(r'class MikroTikCardGenerator.*?:', content)
        if not class_match:
            print("❌ لم يتم العثور على كلاس MikroTikCardGenerator")
            return False
        
        print("✅ تم العثور على الكلاس الرئيسي")
        
        # محاكاة البيانات
        print("\n📝 محاكاة بيانات الكرت الواحد مع فشل جزئي:")
        
        # محاكاة إحصائيات
        success_count = 2
        failed_count = 1
        total_count = 3
        system_type = 'hotspot'
        
        print(f"   - success_count: {success_count}")
        print(f"   - failed_count: {failed_count}")
        print(f"   - total_count: {total_count}")
        print(f"   - system_type: {system_type}")
        
        # محاكاة الكروت الناجحة
        successful_cards = [
            {'username': 'user001', 'password': 'pass001'},
            {'username': 'user002', 'password': 'pass002'}
        ]
        
        # محاكاة الكروت الفاشلة
        failed_cards = [
            {'username': 'user003', 'error': 'مستخدم مكرر'}
        ]
        
        print(f"   - successful_cards: {[card['username'] for card in successful_cards]}")
        print(f"   - failed_cards: {[card['username'] for card in failed_cards]}")
        
        # محاكاة الشروط
        print("\n🔍 تحقق من شروط ظهور الزر:")
        
        condition1 = failed_count > 0
        condition2 = success_count > 0
        condition3 = system_type == 'hotspot'
        condition4 = len(successful_cards) > 0
        
        print(f"   ✅ failed_count > 0: {condition1}")
        print(f"   ✅ success_count > 0: {condition2}")
        print(f"   ✅ system_type == 'hotspot': {condition3}")
        print(f"   ✅ len(successful_cards) > 0: {condition4}")
        
        all_conditions = condition1 and condition2 and condition3 and condition4
        print(f"\n🎯 جميع الشروط مستوفاة: {all_conditions}")
        
        if all_conditions:
            print("✅ الزر يجب أن يظهر في هذه الحالة!")
            
            # محاكاة النص المتوقع
            expected_text = f"🗑️ حذف الكروت الناجحة المرسلة للميكروتيك ({success_count} كرت)"
            expected_callback = f"single_card_delete_successful_{success_count}"
            
            print(f"\n📝 النص المتوقع للزر:")
            print(f"   - النص: {expected_text}")
            print(f"   - callback_data: {expected_callback}")
            
            return True
        else:
            print("❌ الشروط غير مستوفاة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في المحاكاة: {str(e)}")
        return False

def check_button_logic_in_code():
    """التحقق من منطق الزر في الكود"""
    print("\n🔍 التحقق من منطق الزر في الكود...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_details_to_telegram
    import re
    func_match = re.search(r'def send_single_card_details_to_telegram.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_details_to_telegram")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من وجود منطق الزر المحسن
    required_elements = [
        'show_delete_successful_button.*=.*False',
        'تشخيص شروط زر حذف الكروت الناجحة',
        'failed_count.*>.*0',
        'success_count.*>.*0',
        'system_type.*hotspot',
        'إنشاء قائمة الكروت الناجحة',
        'generated_credentials',
        'single_card_successful_cards.*=',
        'حذف الكروت الناجحة المرسلة للميكروتيك',
        'single_card_delete_successful_'
    ]
    
    missing_elements = []
    for element in required_elements:
        if re.search(element, func_code, re.IGNORECASE):
            print(f"✅ عنصر موجود: {element}")
        else:
            print(f"❌ عنصر مفقود: {element}")
            missing_elements.append(element)
    
    if not missing_elements:
        print("✅ جميع العناصر المطلوبة موجودة في الكود!")
        return True
    else:
        print(f"❌ عناصر مفقودة: {len(missing_elements)}")
        return False

def check_data_saving_logic():
    """التحقق من منطق حفظ البيانات"""
    print("\n🔍 التحقق من منطق حفظ البيانات...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_to_mikrotik_silent
    import re
    func_match = re.search(r'def send_single_card_to_mikrotik_silent.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_to_mikrotik_silent")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من منطق حفظ البيانات المحسن
    required_elements = [
        'تشخيص حفظ الكروت الناجحة',
        'successful_cards count',
        'حفظ دائماً إذا كان هناك كروت ناجحة',
        'إذا لم نجد في successful_cards، استخدم generated_credentials',
        'single_card_successful_cards.*=.*successful_usernames',
        'قائمة الكروت الناجحة المحفوظة'
    ]
    
    missing_elements = []
    for element in required_elements:
        if re.search(element, func_code, re.IGNORECASE):
            print(f"✅ عنصر موجود: {element}")
        else:
            print(f"❌ عنصر مفقود: {element}")
            missing_elements.append(element)
    
    if not missing_elements:
        print("✅ جميع عناصر حفظ البيانات موجودة!")
        return True
    else:
        print(f"❌ عناصر مفقودة: {len(missing_elements)}")
        return False

def main():
    """تشغيل جميع الاختبارات"""
    print("🧪 بدء اختبار محاكاة الكرت الواحد مع فشل جزئي")
    print("="*60)
    
    tests = [
        ("محاكاة حالة فشل جزئي", simulate_single_card_partial_failure),
        ("التحقق من منطق الزر", check_button_logic_in_code),
        ("التحقق من حفظ البيانات", check_data_saving_logic)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                print(f"✅ {test_name}: نجح")
                passed += 1
            else:
                print(f"❌ {test_name}: فشل")
                failed += 1
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {str(e)}")
            failed += 1
        print("-" * 40)
    
    print("\n" + "="*60)
    print("📊 نتائج الاختبار:")
    print(f"✅ نجح: {passed}")
    print(f"❌ فشل: {failed}")
    print(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
    
    if failed == 0:
        print("🎉 جميع الاختبارات نجحت!")
        print("💡 الزر يجب أن يظهر الآن في حالة الفشل الجزئي")
        
        print("\n🎯 التوصيات للاختبار:")
        print("1. جرب إنشاء عدة كروت (3-5 كروت)")
        print("2. تأكد من أن بعض الكروت ستفشل (مثل أسماء مكررة)")
        print("3. تأكد من أن النظام هو HotSpot")
        print("4. راقب رسائل السجل للتشخيص")
        
    else:
        print("⚠️ بعض الاختبارات فشلت - يحتاج مراجعة.")
    
    return failed == 0

if __name__ == "__main__":
    main()
